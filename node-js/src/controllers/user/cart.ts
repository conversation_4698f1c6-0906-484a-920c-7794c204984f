import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  addToCartService,
  clearCartService,
  getOrCreateCartService,
  removeFromCartService,
  updateCartItemService,
} from "~/services/user/cart";
import {
  addToCartBodySchema,
  getCartQuerySchema,
  removeFromCartParamsSchema,
  updateCartItemBodySchema,
} from "~/validators/user/cart";

async function getCart(request: Request, response: Response) {
  try {
    const { includeDetails } = getCartQuerySchema.parse(request.query);

    const { cart } = await getOrCreateCartService({
      userId: request.user.id,
    });

    return response.success(
      {
        data: { cart },
      },
      {
        message: "Cart fetched successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function addToCart(request: Request, response: Response) {
  try {
    const { productId, quantity, replaceExisting } = addToCartBodySchema.parse(
      request.body,
    );

    const { cartItem } = await addToCartService({
      userId: request.user.id,
      productId,
      quantity,
      replaceExisting,
    });

    return response.success(
      {
        data: { cartItem },
      },
      {
        message: "Item added to cart successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function updateCartItem(request: Request, response: Response) {
  try {
    const { productId } = removeFromCartParamsSchema.parse(request.params);
    const { quantity } = updateCartItemBodySchema.parse(request.body);

    const result = await updateCartItemService({
      userId: request.user.id,
      productId,
      quantity,
    });

    if (result.deleted) {
      return response.success(
        {
          data: { deleted: true },
        },
        {
          message: "Item removed from cart successfully",
        },
      );
    }

    return response.success(
      {
        data: { cartItem: result.cartItem },
      },
      {
        message: "Cart item updated successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function removeFromCart(request: Request, response: Response) {
  try {
    const { productId } = removeFromCartParamsSchema.parse(request.params);

    await removeFromCartService({
      userId: request.user.id,
      productId,
    });

    return response.success(
      {
        data: { success: true },
      },
      {
        message: "Item removed from cart successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function clearCart(request: Request, response: Response) {
  try {
    await clearCartService({
      userId: request.user.id,
    });

    return response.success(
      {
        data: { success: true },
      },
      {
        message: "Cart cleared successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { getCart, addToCart, updateCartItem, removeFromCart, clearCart };
