import { Router } from "express";

import {
  addToCart,
  clearCart,
  getCart,
  removeFromCart,
  updateCartItem,
} from "~/controllers/user/cart";

const cartRouter = Router();

cartRouter.get("/", getCart);

cartRouter.post("/", addToCart);

cartRouter.put("/:productId", updateCartItem);

cartRouter.delete("/:productId", removeFromCart);

cartRouter.delete("/", clearCart);

export { cartRouter };
