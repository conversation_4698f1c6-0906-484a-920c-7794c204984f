import * as zod from "zod";

const addToCartBodySchema = zod.object({
  productId: zod
    .string({
      message: "Product ID must be a string",
    })
    .length(24, {
      message: "Product ID must be a 24-character string",
    }),
  quantity: zod
    .number({
      message: "Quantity must be a number",
    })
    .int({
      message: "Quantity must be an integer",
    })
    .min(1, {
      message: "Quantity must be a positive number",
    }),
  replaceExisting: zod
    .boolean({
      message: "Replace existing must be a boolean",
    })
    .default(false),
});

const updateCartItemBodySchema = zod.object({
  quantity: zod
    .number({
      message: "Quantity must be a number",
    })
    .int({
      message: "Quantity must be an integer",
    })
    .min(0, {
      message: "Quantity must be a non-negative number",
    }),
});

const removeFromCartParamsSchema = zod.object({
  productId: zod
    .string({
      message: "Product ID must be a string",
    })
    .length(24, {
      message: "Product ID must be a 24-character string",
    }),
});

const getCartQuerySchema = zod.object({
  includeDetails: zod
    .enum(["true", "false"], {
      message: "Include details must be 'true' or 'false'",
    })
    .transform((val) => val === "true")
    .default("true"),
});

export {
  addToCartBodySchema,
  updateCartItemBodySchema,
  removeFromCartParamsSchema,
  getCartQuerySchema,
};
