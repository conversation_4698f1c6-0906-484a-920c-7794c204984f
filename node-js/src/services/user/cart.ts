import { BadResponse, NotFoundResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { publicSelector } from "~/selectors/public";
import { vendorSelector } from "~/selectors/vendor";

/**
 * Get or create cart for a user
 */
async function getOrCreateCartService({ userId }: { userId: string }) {
  let cart = await prisma.cart.findUnique({
    where: { userId },
    include: {
      items: {
        include: {
          product: {
            select: {
              ...publicSelector.product,
              category: {
                select: publicSelector.category,
              },
              vendor: {
                select: vendorSelector.profile,
              },
            },
          },
        },
        orderBy: { createdAt: "desc" },
      },
    },
  });

  if (!cart) {
    cart = await prisma.cart.create({
      data: { userId },
      include: {
        items: {
          include: {
            product: {
              select: {
                ...publicSelector.product,
                category: {
                  select: publicSelector.category,
                },
                vendor: {
                  select: vendorSelector.profile,
                },
              },
            },
          },
          orderBy: { createdAt: "desc" },
        },
      },
    });
  }

  return { cart };
}

/**
 * Add item to cart with vendor compatibility check
 */
async function addToCartService({
  userId,
  productId,
  quantity,
  replaceExisting = false,
}: {
  userId: string;
  productId: string;
  quantity: number;
  replaceExisting?: boolean;
}) {
  // Get the product with vendor information
  const product = await prisma.product.findUnique({
    where: { id: productId, isDeleted: false },
    select: {
      id: true,
      stock: true,
      vendor: {
        select: { id: true },
      },
    },
  });

  if (!product) {
    throw new NotFoundResponse("Product not found");
  }

  if (product.stock < quantity) {
    throw new BadResponse("Insufficient stock available");
  }

  // Get or create cart
  const { cart } = await getOrCreateCartService({ userId });

  // Check vendor compatibility if not replacing existing cart
  if (!replaceExisting && cart.items.length > 0) {
    const existingVendorId = cart.items[0]?.product.vendor.id;
    if (existingVendorId && existingVendorId !== product.vendor.id) {
      throw new BadResponse(
        "Cannot add products from different vendors to the same cart"
      );
    }
  }

  // If replacing existing, clear the cart first
  if (replaceExisting) {
    await prisma.cartItem.deleteMany({
      where: { cartId: cart.id },
    });
  }

  // Check if item already exists in cart
  const existingItem = await prisma.cartItem.findUnique({
    where: {
      cartId_productId: {
        cartId: cart.id,
        productId,
      },
    },
  });

  let cartItem;
  if (existingItem) {
    const newQuantity = replaceExisting
      ? quantity
      : existingItem.quantity + quantity;

    if (newQuantity > product.stock) {
      throw new BadResponse("Total quantity exceeds available stock");
    }

    cartItem = await prisma.cartItem.update({
      where: { id: existingItem.id },
      data: { quantity: newQuantity },
      include: {
        product: {
          select: {
            ...publicSelector.product,
            category: {
              select: publicSelector.category,
            },
            vendor: {
              select: vendorSelector.profile,
            },
          },
        },
      },
    });
  } else {
    cartItem = await prisma.cartItem.create({
      data: {
        cartId: cart.id,
        productId,
        quantity,
      },
      include: {
        product: {
          select: {
            ...publicSelector.product,
            category: {
              select: publicSelector.category,
            },
            vendor: {
              select: vendorSelector.profile,
            },
          },
        },
      },
    });
  }

  return { cartItem };
}

/**
 * Update cart item quantity
 */
async function updateCartItemService({
  userId,
  productId,
  quantity,
}: {
  userId: string;
  productId: string;
  quantity: number;
}) {
  const cart = await prisma.cart.findUnique({
    where: { userId },
    select: { id: true },
  });

  if (!cart) {
    throw new NotFoundResponse("Cart not found");
  }

  const cartItem = await prisma.cartItem.findUnique({
    where: {
      cartId_productId: {
        cartId: cart.id,
        productId,
      },
    },
    include: {
      product: {
        select: { stock: true },
      },
    },
  });

  if (!cartItem) {
    throw new NotFoundResponse("Cart item not found");
  }

  if (quantity === 0) {
    await prisma.cartItem.delete({
      where: { id: cartItem.id },
    });
    return { deleted: true };
  }

  if (quantity > cartItem.product.stock) {
    throw new BadResponse("Quantity exceeds available stock");
  }

  const updatedCartItem = await prisma.cartItem.update({
    where: { id: cartItem.id },
    data: { quantity },
    include: {
      product: {
        select: {
          ...publicSelector.product,
          category: {
            select: publicSelector.category,
          },
          vendor: {
            select: vendorSelector.profile,
          },
        },
      },
    },
  });

  return { cartItem: updatedCartItem };
}

/**
 * Remove item from cart
 */
async function removeFromCartService({
  userId,
  productId,
}: {
  userId: string;
  productId: string;
}) {
  const cart = await prisma.cart.findUnique({
    where: { userId },
    select: { id: true },
  });

  if (!cart) {
    throw new NotFoundResponse("Cart not found");
  }

  const cartItem = await prisma.cartItem.findUnique({
    where: {
      cartId_productId: {
        cartId: cart.id,
        productId,
      },
    },
  });

  if (!cartItem) {
    throw new NotFoundResponse("Cart item not found");
  }

  await prisma.cartItem.delete({
    where: { id: cartItem.id },
  });

  return { success: true };
}

/**
 * Clear entire cart
 */
async function clearCartService({ userId }: { userId: string }) {
  const cart = await prisma.cart.findUnique({
    where: { userId },
    select: { id: true },
  });

  if (!cart) {
    return { success: true };
  }

  await prisma.cartItem.deleteMany({
    where: { cartId: cart.id },
  });

  return { success: true };
}

export {
  getOrCreateCartService,
  addToCartService,
  updateCartItemService,
  removeFromCartService,
  clearCartService,
};
