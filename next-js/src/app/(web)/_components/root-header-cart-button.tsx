"use client";

import Image from "next/image";
import Link from "next/link";
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
import { useEffect, useMemo, useState } from "react";

import { useStore } from "@nanostores/react";

import {
  Loader2Icon,
  MinusIcon,
  PlusIcon,
  ShoppingBagIcon,
  ShoppingCartIcon,
  XIcon,
} from "lucide-react";

import { EmptyState } from "~/components/layout/empty-state";

import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";

import { toast } from "sonner";
import { Separator } from "~/components/ui/separator";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import { cn, formatPrice } from "~/lib/utils";
import { $cart, cartAPI } from "~/stores/cart";

export function RootHeaderCartButton() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const params = useParams();
  const router = useRouter();
  const cart = useStore($cart);
  const { auth, token } = useAuthContext();

  const [isCartPopoverOpen, setIsCartPopoverOpen] = useState(false);

  // biome-ignore lint/correctness/useExhaustiveDependencies: To close the popover when the path changes
  useEffect(() => {
    setIsCartPopoverOpen(false);
  }, [searchParams, pathname, params]);

  // Fetch cart when user is authenticated
  useEffect(() => {
    if (auth?.role === "USER" && token) {
      cartAPI.fetchCart(token);
    }
  }, [auth, token]);

  const { totalQuantity, totalPrice } = useMemo(() => {
    let quantity = 0;
    let price = 0;
    for (const item of cart.items) {
      quantity += item.quantity;
      const itemPrice = item.salePrice ?? item.price;
      price += itemPrice * item.quantity;
    }
    return { totalQuantity: quantity, totalPrice: price };
  }, [cart.items]);

  if (auth && auth.role !== "USER") {
    return null;
  }

  const handleUpdateQuantity = async (itemId: string, change: number) => {
    if (!token) {
      toast.error("Please sign in to manage your cart");
      router.push(routes.app.auth.signIn.url());
      return;
    }

    const item = cart.items.find((item) => item.id === itemId);
    if (!item) return;

    const newQuantity = Math.max(0, item.quantity + change);

    try {
      if (newQuantity === 0) {
        await cartAPI.removeFromCart(token, itemId);
        toast.success("Item removed from cart");
      } else {
        await cartAPI.updateCartItem(token, itemId, newQuantity);
        toast.success("Cart updated");
      }
    } catch (error: any) {
      console.error("Failed to update cart:", error);
      toast.error(error.message || "Failed to update cart");
    }
  };

  const handleIncreaseQuantity = (itemId: string) => {
    handleUpdateQuantity(itemId, 1);
  };

  const handleDecreaseQuantity = (itemId: string) => {
    handleUpdateQuantity(itemId, -1);
  };

  const handleRemoveItem = async (itemId: string) => {
    if (!token) {
      toast.error("Please sign in to manage your cart");
      router.push(routes.app.auth.signIn.url());
      return;
    }

    try {
      await cartAPI.removeFromCart(token, itemId);
      toast.success("Item removed from cart");
    } catch (error: any) {
      console.error("Failed to remove item:", error);
      toast.error(error.message || "Failed to remove item");
    }
  };

  return (
    <Popover open={isCartPopoverOpen} onOpenChange={setIsCartPopoverOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className={cn("relative")}
          aria-label={`Shopping cart with ${totalQuantity} items`}
          disabled={auth ? auth.role !== "USER" : false}
        >
          <ShoppingCartIcon className="size-5" />
          {totalQuantity > 0 && (
            <Badge
              variant="destructive"
              className={cn(
                "absolute -top-2 -right-2 h-5 min-w-[1.25rem] px-1 flex items-center justify-center rounded-full text-xs"
              )}
            >
              {totalQuantity}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className={cn(
          "w-80 space-y-0 p-0 mt-2 mr-4 max-h-[80vh] overflow-visible"
        )}
        align="center"
      >
        <div className={cn("p-4")}>
          <h3 className={cn("text-lg font-medium")}>Shopping Cart</h3>
        </div>
        <Separator />
        {cart.items.length === 0 ? (
          <div className={cn("p-4")}>
            <EmptyState
              icon={ShoppingBagIcon}
              title="Your cart is empty"
              description="Add some products to your cart to checkout."
              action={{
                label: "Browse Products",
                onClick: () => {
                  setIsCartPopoverOpen(false);
                  router.push(routes.app.public.products.url());
                },
              }}
              className="border-none bg-transparent"
            />
          </div>
        ) : (
          <>
            <div className={cn("max-h-[300px] w-full overflow-y-auto")}>
              <div className={cn("p-4 space-y-4")}>
                {cart.items.map((item) => {
                  const itemPrice = item.salePrice ?? item.price;
                  const itemSubtotal = itemPrice * item.quantity;
                  return (
                    <div key={item.id} className={cn("flex items-start gap-4")}>
                      <div
                        className={cn(
                          "relative size-16 flex-shrink-0 overflow-hidden rounded-md border"
                        )}
                      >
                        <Image
                          src={`${process.env.NEXT_PUBLIC_FILE_URL}/${item.pictureIds[0]}`}
                          alt={item.name}
                          fill
                          sizes="64px"
                          className={cn("object-cover")}
                        />
                      </div>
                      <div className={cn("flex-grow space-y-1")}>
                        <p
                          className={cn(
                            "text-sm font-medium leading-tight line-clamp-2"
                          )}
                        >
                          {item.name}
                        </p>
                        <div className={cn("flex items-center gap-2")}>
                          <Button
                            variant="outline"
                            size="icon"
                            className="size-6"
                            onClick={() => handleDecreaseQuantity(item.id)}
                            aria-label={`Decrease quantity of ${item.name}`}
                            disabled={
                              item.quantity <= 1 ||
                              (auth ? auth.role !== "USER" : false) ||
                              cartAPI.isItemUpdating(item.id) ||
                              cartAPI.isAnyItemLoading()
                            }
                          >
                            {cartAPI.isItemUpdating(item.id) ? (
                              <Loader2Icon className="size-3 animate-spin" />
                            ) : (
                              <MinusIcon className="size-3" />
                            )}
                          </Button>
                          <span className="text-sm font-medium w-4 text-center">
                            {item.quantity}
                          </span>
                          <Button
                            variant="outline"
                            size="icon"
                            className="size-6"
                            onClick={() => handleIncreaseQuantity(item.id)}
                            aria-label={`Increase quantity of ${item.name}`}
                            disabled={
                              (auth ? auth.role !== "USER" : false) ||
                              cartAPI.isItemUpdating(item.id) ||
                              cartAPI.isAnyItemLoading()
                            }
                          >
                            {cartAPI.isItemUpdating(item.id) ? (
                              <Loader2Icon className="size-3 animate-spin" />
                            ) : (
                              <PlusIcon className="size-3" />
                            )}
                          </Button>
                        </div>
                        <p className={cn("text-xs text-muted-foreground")}>
                          @ {formatPrice(itemPrice)} each
                        </p>
                        <p className={cn("text-sm font-medium")}>
                          Subtotal: {formatPrice(itemSubtotal)}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        className={cn(
                          "size-6 flex-shrink-0 text-muted-foreground hover:text-destructive"
                        )}
                        onClick={() => handleRemoveItem(item.id)}
                        aria-label={`Remove ${item.name} from cart`}
                        disabled={
                          (auth ? auth.role !== "USER" : false) ||
                          cartAPI.isItemRemoving(item.id) ||
                          cartAPI.isAnyItemLoading()
                        }
                      >
                        {cartAPI.isItemRemoving(item.id) ? (
                          <Loader2Icon className="size-4 animate-spin" />
                        ) : (
                          <XIcon className="size-4" />
                        )}
                      </Button>
                    </div>
                  );
                })}
              </div>
            </div>
            <Separator />
          </>
        )}
        {cart.items.length > 0 && (
          <div className={cn("p-4 space-y-3")}>
            <div className={cn("flex justify-between text-base font-medium")}>
              <p>Total</p>
              <p>{formatPrice(totalPrice)}</p>
            </div>
            <p className={cn("text-xs text-muted-foreground")}>
              Shipping and taxes calculated at checkout.
            </p>
            <div className={cn("flex flex-col gap-2 sm:flex-row")}>
              <Button
                variant="default-gradient"
                className={cn("flex-1")}
                asChild
                disabled={auth ? auth.role !== "USER" : false}
              >
                <Link href={routes.app.user.checkout.url()}>Checkout</Link>
              </Button>
            </div>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
