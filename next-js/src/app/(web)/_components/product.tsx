"use client";

import type {
  PublicCategoryType,
  PublicProductType,
  VendorProfileType,
} from "~/lib/types";

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

import { useStore } from "@nanostores/react";

import { VendorConfirmationDialog } from "~/app/(web)/_components/vendor-confirmation-dialog";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardFooter } from "~/components/ui/card";
import { useAuthContext } from "~/context/auth";
import { checkVendorCompatibility } from "~/lib/cart-utils";
import { routes } from "~/lib/routes";
import { cn, formatPrice } from "~/lib/utils";
import { $cart, cartAPI } from "~/stores/cart";

interface ProductProps {
  product: PublicProductType & {
    category: PublicCategoryType;
    vendor: VendorProfileType;
  };
}

export function Product({ product }: Readonly<ProductProps>) {
  const cart = useStore($cart);
  const [showVendorDialog, setShowVendorDialog] = useState(false);
  const [vendorDialogData, setVendorDialogData] = useState<{
    currentVendor: VendorProfileType | null;
    newVendor: VendorProfileType;
  } | null>(null);
  const { auth, token } = useAuthContext();

  return (
    <>
      <Card className={cn("p-0 gap-0 relative")}>
        {product.isVerified && (
          <Badge className="absolute top-4 right-4 bg-green-100 text-green-800 border-green-300">
            EcoBuilt Verified
          </Badge>
        )}
        <Link href={routes.app.public.products.url(product.id)}>
          <CardContent className={cn("py-5 px-10")}>
            <Image
              src={`${process.env.NEXT_PUBLIC_FILE_URL}/${product.pictureIds[0]}`}
              alt={product.name}
              width={240}
              height={240}
              className={cn("h-60 w-full object-contain rounded-t-xl")}
            />
          </CardContent>
        </Link>
        <CardFooter
          className={cn("p-4 flex-col items-stretch gap-2 bg-gray-50")}
        >
          <div>
            <h4 className={cn("text-[10px] uppercase")}>
              {product.vendor.name}
            </h4>
            <h3 className={cn("text-[16px]")}>{product.name}</h3>
          </div>
          <div className={cn("flex justify-between items-center")}>
            <div className={cn("-space-y-1")}>
              <p className={cn("space-x-1 text-lg font-medium")}>
                <span>
                  {product.salePrice
                    ? formatPrice(product.salePrice)
                    : formatPrice(product.price)}
                </span>
                {product.salePrice && (
                  <span
                    className={cn("text-muted-foreground text-sm line-through")}
                  >
                    {formatPrice(product.price)}
                  </span>
                )}
              </p>
              <p className={cn("text-[10px] text-muted-foreground")}>
                Excl. VAT
              </p>
            </div>
            <Button
              variant="default-gradient"
              size="default"
              onClick={async () => {
                if (!token) return;

                const { isSameVendor, currentVendor, newVendor } =
                  checkVendorCompatibility(cart.items, product);

                if (isSameVendor) {
                  try {
                    await cartAPI.addToCart(token, product.id, 1, false);
                  } catch (error) {
                    console.error("Failed to add to cart:", error);
                    // You might want to show a toast notification here
                  }
                } else if (currentVendor && newVendor) {
                  setVendorDialogData({ currentVendor, newVendor });
                  setShowVendorDialog(true);
                }
              }}
              disabled={auth ? auth.role !== "USER" : false}
            >
              Purchase
            </Button>
          </div>
        </CardFooter>
      </Card>

      {vendorDialogData?.currentVendor && (
        <VendorConfirmationDialog
          isOpen={showVendorDialog}
          onOpenChange={setShowVendorDialog}
          currentVendor={vendorDialogData.currentVendor}
          newVendor={vendorDialogData.newVendor}
          onConfirm={async () => {
            if (token) {
              try {
                await cartAPI.addToCart(token, product.id, 1, true);
              } catch (error) {
                console.error("Failed to replace cart:", error);
              }
            }
            setShowVendorDialog(false);
          }}
          onCancel={() => {
            setShowVendorDialog(false);
          }}
        />
      )}
    </>
  );
}
