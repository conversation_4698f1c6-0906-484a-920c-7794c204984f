import type {
  CartItemType,
  PublicCategoryType,
  VendorProfileType,
} from "~/lib/types";

import { atom } from "nanostores";
import axios from "axios";
import { routes } from "~/lib/routes";

export interface CartState {
  items: (CartItemType & {
    category: PublicCategoryType;
    vendor: VendorProfileType;
  })[];
  isLoading: boolean;
  error: string | null;
}

export const $cart = atom<CartState>({
  items: [],
  isLoading: false,
  error: null,
});

// Cart API functions
export const cartAPI = {
  async fetchCart(token: string | null) {
    if (!token) return { items: [] };

    try {
      $cart.set({ ...$cart.get(), isLoading: true, error: null });

      const response = await axios.get(routes.api.user.cart.url(), {
        headers: { Authorization: `Bearer ${token}` },
      });

      const cartData = response.data.data.cart;
      const items = cartData.items.map((item: any) => ({
        ...item.product,
        quantity: item.quantity,
      }));

      $cart.set({ items, isLoading: false, error: null });
      return { items };
    } catch (error) {
      console.error("Failed to fetch cart:", error);
      $cart.set({
        ...$cart.get(),
        isLoading: false,
        error: "Failed to fetch cart",
      });
      return { items: [] };
    }
  },

  async addToCart(
    token: string | null,
    productId: string,
    quantity: number,
    replaceExisting = false
  ) {
    if (!token) throw new Error("Authentication required");

    try {
      $cart.set({ ...$cart.get(), isLoading: true, error: null });

      await axios.post(
        routes.api.user.cart.url(),
        { productId, quantity, replaceExisting },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      // Refresh cart after adding
      await this.fetchCart(token);
    } catch (error: any) {
      console.error("Failed to add to cart:", error);
      const errorMessage =
        error.response?.data?.info?.message || "Failed to add to cart";
      $cart.set({ ...$cart.get(), isLoading: false, error: errorMessage });
      throw new Error(errorMessage);
    }
  },

  async updateCartItem(
    token: string | null,
    productId: string,
    quantity: number
  ) {
    if (!token) throw new Error("Authentication required");

    try {
      $cart.set({ ...$cart.get(), isLoading: true, error: null });

      await axios.put(
        routes.api.user.cart.url(productId),
        { quantity },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      // Refresh cart after updating
      await this.fetchCart(token);
    } catch (error: any) {
      console.error("Failed to update cart item:", error);
      const errorMessage =
        error.response?.data?.info?.message || "Failed to update cart item";
      $cart.set({ ...$cart.get(), isLoading: false, error: errorMessage });
      throw new Error(errorMessage);
    }
  },

  async removeFromCart(token: string | null, productId: string) {
    if (!token) throw new Error("Authentication required");

    try {
      $cart.set({ ...$cart.get(), isLoading: true, error: null });

      await axios.delete(routes.api.user.cart.url(productId), {
        headers: { Authorization: `Bearer ${token}` },
      });

      // Refresh cart after removing
      await this.fetchCart(token);
    } catch (error: any) {
      console.error("Failed to remove from cart:", error);
      const errorMessage =
        error.response?.data?.info?.message || "Failed to remove from cart";
      $cart.set({ ...$cart.get(), isLoading: false, error: errorMessage });
      throw new Error(errorMessage);
    }
  },

  async clearCart(token: string | null) {
    if (!token) throw new Error("Authentication required");

    try {
      $cart.set({ ...$cart.get(), isLoading: true, error: null });

      await axios.delete(routes.api.user.cart.url(), {
        headers: { Authorization: `Bearer ${token}` },
      });

      $cart.set({ items: [], isLoading: false, error: null });
    } catch (error: any) {
      console.error("Failed to clear cart:", error);
      const errorMessage =
        error.response?.data?.info?.message || "Failed to clear cart";
      $cart.set({ ...$cart.get(), isLoading: false, error: errorMessage });
      throw new Error(errorMessage);
    }
  },
};
